"""
V2 Update Record Function

Database function to update records in any table with flexible input handling.
This implements the V2 pattern for database UPDATE operations.
"""

from typing import Dict, Any, Optional
from sqlalchemy.sql import text

from ...base_function import BaseSystemFunction
from ...models import SystemFunctionInput, FunctionExecutionContext, FunctionCategory

class UpdateRecordFunction(BaseSystemFunction):
    """
    V2 Update Record function with standardized input handling.
    
    This function can update records in any table with flexible input processing.
    It properly handles all input parameters and converts them to UPDATE statements.
    """
    
    def get_category(self) -> FunctionCategory:
        return FunctionCategory.DATABASE
    
    def get_required_inputs(self) -> list[str]:
        """Required inputs for update_record function"""
        return ["table"]  # Only table is required
    
    def get_optional_inputs(self) -> list[str]:
        """Optional inputs for update_record function"""
        return [
            "data",              # Dict of column: value data to update
            "where",             # WHERE conditions for update
            "where_column",      # Single column for WHERE condition
            "where_value",       # Single value for WHERE condition
            "return_columns",    # Columns to return after update
            
            # Legacy parameter support (for backward compatibility)
            "entity",            # Maps to table
            "entity_id",         # Can be used as primary key
            "id",                # Primary key value for WHERE condition
            
            # Dynamic data parameters (any other parameter becomes update data)
        ]
    
    def execute_function(self, input_data: SystemFunctionInput, context: FunctionExecutionContext) -> Dict[str, Any]:
        """
        Execute update_record with flexible parameter handling.
        
        This function intelligently handles all input parameters and converts them
        to appropriate UPDATE statements.
        """
        self.log_info("🔍 Starting update_record execution", context)
        
        # DETAILED LOGGING: Log all input parameters received
        self.log_info(f"🔍 DETAILED: All input parameters received:", context)
        for key, value in input_data.input_values.items():
            self.log_info(f"🔍 DETAILED:   {key} = {value}", context)
        
        # Get table name (required)
        table = self.get_input_value(input_data, "table")
        if not table:
            # Try legacy 'entity' parameter
            table = self.get_input_value(input_data, "entity")
        
        if not table:
            raise ValueError("Table name is required (provide 'table' or 'entity' parameter)")
        
        self.log_info(f"📋 Target table: {table}", context)
        
        # Build the update data
        update_data = self._build_update_data(input_data, context)
        
        if not update_data:
            raise ValueError("No data provided for update operation")
        
        # Build WHERE conditions
        where_conditions = self._build_where_conditions(input_data, context)
        
        if not where_conditions:
            raise ValueError("No WHERE conditions provided for update operation")
        
        # Build the UPDATE query
        query_parts = self._build_update_query(table, update_data, where_conditions, input_data, context)
        
        # Execute query
        self.log_info(f"🔍 Executing query: {query_parts['query']}", context)
        self.log_info(f"🔍 Query parameters: {query_parts['parameters']}", context)
        
        try:
            result = self.db.execute(text(query_parts['query']), query_parts['parameters'])
            
            # Handle return data
            if query_parts['has_returning']:
                # Fetch the returned rows
                returned_rows = result.fetchall()
                if returned_rows:
                    return_data = []
                    for row in returned_rows:
                        if hasattr(row, '_asdict'):
                            return_data.append(row._asdict())
                        else:
                            return_data.append(dict(row))
                    
                    self.log_info(f"✅ Records updated in {table} with returned data: {return_data}", context)
                    return {"status": "updated", "table": table, "data": return_data, "rows_affected": len(return_data)}
                else:
                    rows_affected = result.rowcount
                    self.log_info(f"✅ Records updated in {table} ({rows_affected} rows affected, no data returned)", context)
                    return {"status": "updated", "table": table, "rows_affected": rows_affected}
            else:
                # No RETURNING clause, just confirm update
                rows_affected = result.rowcount
                self.log_info(f"✅ Records updated in {table} ({rows_affected} rows affected)", context)
                return {"status": "updated", "table": table, "rows_affected": rows_affected}
            
        except Exception as e:
            self.log_error(f"💥 Database update failed: {str(e)}", context)
            raise
    
    def _build_update_data(self, input_data: SystemFunctionInput, context: FunctionExecutionContext) -> Dict[str, Any]:
        """Build update data from input parameters"""
        
        update_data = {}
        
        # Process explicit data parameter
        explicit_data = self.get_input_value(input_data, "data", {})
        if isinstance(explicit_data, dict):
            update_data.update(explicit_data)
        
        # Process any other input values as potential column data
        # This handles dynamic parameters passed from LO execution
        excluded_params = {
            "table", "entity", "data", "where", "where_column", "where_value", 
            "return_columns", "id", "entity_id",
            "go_id", "lo_id", "user_id", "tenant_id", "instance_id",
            "primary_entity_id", "primary_attribute_id", "function_params", "execution_context"
        }
        
        for key, val in input_data.input_values.items():
            if key not in excluded_params and val is not None:
                # Convert parameter name to potential column name
                column_name = self._convert_param_to_column(key)
                update_data[column_name] = val
                self.log_debug(f"Added update data: {column_name} = {val}", context)
        
        self.log_info(f"📋 Update data: {update_data}", context)
        return update_data
    
    def _build_where_conditions(self, input_data: SystemFunctionInput, context: FunctionExecutionContext) -> Dict[str, Any]:
        """Build WHERE conditions from input parameters"""
        
        where_conditions = {}
        
        # Process explicit where parameter (dict format)
        explicit_where = self.get_input_value(input_data, "where", {})
        if isinstance(explicit_where, dict):
            where_conditions.update(explicit_where)
        
        # Process single where_column/where_value pair
        where_column = self.get_input_value(input_data, "where_column")
        where_value = self.get_input_value(input_data, "where_value")
        if where_column and where_value is not None:
            where_conditions[where_column] = where_value
        
        # Process legacy id parameter
        id_value = self.get_input_value(input_data, "id")
        if id_value is not None:
            where_conditions["id"] = id_value
        
        # Process entity_id parameter
        entity_id_value = self.get_input_value(input_data, "entity_id")
        if entity_id_value is not None:
            where_conditions["entity_id"] = entity_id_value
        
        # DEFAULT RULE: If no WHERE conditions found, fetch primary key from entity and use its value from input stack
        if not where_conditions:
            table = self.get_input_value(input_data, "table") or self.get_input_value(input_data, "entity")
            if table:
                primary_key_conditions = self._get_primary_key_conditions(table, input_data, context)
                where_conditions.update(primary_key_conditions)
        
        self.log_info(f"📋 WHERE conditions: {where_conditions}", context)
        return where_conditions
    
    def _get_primary_key_conditions(self, entity_id: str, input_data: SystemFunctionInput, context: FunctionExecutionContext) -> Dict[str, Any]:
        """Get primary key conditions for the entity from input stack"""
        
        try:
            self.log_info(f"🔍 Fetching primary key for entity: {entity_id}", context)
            
            # CRITICAL FIX: If entity_id looks like a table name (e.g., e7_leaveapplication), 
            # convert it back to entity ID format (E7)
            actual_entity_id = entity_id
            if "_" in entity_id and not entity_id.startswith("E"):
                # Convert table name back to entity ID (e.g., e7_leaveapplication -> E7)
                parts = entity_id.split("_")
                if len(parts) >= 1 and parts[0].startswith("e") and parts[0][1:].isdigit():
                    actual_entity_id = f"E{parts[0][1:]}"
                    self.log_info(f"🔍 Converted table name {entity_id} to entity ID {actual_entity_id}", context)
            
            # Get primary key attribute for this entity
            pk_query = """
            SELECT ea.attribute_id, ea.name as attribute_name
            FROM workflow_runtime.entity_attributes ea
            WHERE ea.entity_id = :entity_id AND ea.is_primary_key = true
            LIMIT 1
            """
            
            pk_result = self.db.execute(text(pk_query), {"entity_id": actual_entity_id}).fetchone()
            
            if not pk_result:
                self.log_warning(f"No primary key found for entity {entity_id}", context)
                return {}
            
            pk_attribute_name = pk_result.attribute_name
            self.log_info(f"🔍 Primary key attribute: {pk_attribute_name}", context)
            
            # Look for primary key value in input parameters
            pk_value = None
            
            # Check direct parameter name (e.g., "leaveId")
            pk_value = self.get_input_value(input_data, pk_attribute_name)
            
            if pk_value is None:
                # Check lowercase version
                pk_value = self.get_input_value(input_data, pk_attribute_name.lower())
            
            if pk_value is None:
                # Check camelCase version (e.g., "leaveid")
                pk_value = self.get_input_value(input_data, pk_attribute_name.lower().replace("_", ""))
            
            if pk_value is not None:
                # CRITICAL: Convert primary key value to string to avoid data type mismatch
                pk_value_str = str(pk_value)
                self.log_info(f"✅ Found primary key value: {pk_attribute_name} = {pk_value_str} (converted to string)", context)
                return {pk_attribute_name.lower(): pk_value_str}
            else:
                self.log_warning(f"Primary key value not found in input parameters for {pk_attribute_name}", context)
                return {}
                
        except Exception as e:
            self.log_error(f"Error getting primary key conditions: {str(e)}", context)
            return {}
    
    def _convert_param_to_column(self, param_name: str) -> str:
        """
        Convert parameter name to database column name.
        Use parameter names as-is (lowercase) without snake_case conversion.
        """
        # Simply convert to lowercase without snake_case conversion
        return param_name.lower()
    
    def _build_update_query(self, table: str, update_data: Dict[str, Any], where_conditions: Dict[str, Any], input_data: SystemFunctionInput, context: FunctionExecutionContext) -> Dict[str, Any]:
        """Build the UPDATE query from data and conditions"""
        
        if not update_data:
            raise ValueError("No data to update")
        
        if not where_conditions:
            raise ValueError("No WHERE conditions provided")
        
        # Map entity ID to actual table name
        actual_table_name = self._get_actual_table_name(table)
        
        # Add audit columns automatically for UPDATE
        update_data_with_audit = update_data.copy()
        
        # Get user_id from context for audit fields
        user_id = context.user_id if hasattr(context, 'user_id') and context.user_id else 'system'
        
        # Always update the updated_at and updated_by fields
        update_data_with_audit['updated_at'] = 'NOW()'
        update_data_with_audit['updated_by'] = user_id
        
        self.log_info(f"📋 Update data with audit: {update_data_with_audit}", context)
        
        # Build SET clause
        set_clauses = []
        parameters = {}
        param_counter = 0
        
        for column, value in update_data_with_audit.items():
            # Handle special SQL functions like NOW()
            if value == 'NOW()':
                set_clauses.append(f"{column} = NOW()")
            else:
                param_name = f"set_param_{param_counter}"
                set_clauses.append(f"{column} = :{param_name}")
                parameters[param_name] = value
                param_counter += 1
        
        # Build WHERE clause
        where_clauses = []
        for column, value in where_conditions.items():
            param_name = f"where_param_{param_counter}"
            where_clauses.append(f"{column} = :{param_name}")
            parameters[param_name] = value
            param_counter += 1
        
        # Build base UPDATE query
        query = f"""
        UPDATE workflow_runtime.{actual_table_name}
        SET {', '.join(set_clauses)}
        WHERE {' AND '.join(where_clauses)}
        """
        
        # Add RETURNING clause if requested
        return_columns = self.get_input_value(input_data, "return_columns")
        has_returning = False
        
        if return_columns:
            if isinstance(return_columns, str):
                return_columns = [return_columns]
            elif return_columns is True:
                return_columns = ["*"]
            
            if return_columns:
                query += f" RETURNING {', '.join(return_columns)}"
                has_returning = True
        
        return {
            "query": query,
            "parameters": parameters,
            "has_returning": has_returning
        }
    
    def _get_actual_table_name(self, entity_id: str) -> str:
        """Get actual table name from entities table using entity_id"""
        try:
            # Query the entities table to get the actual table name
            query = """
            SELECT table_name FROM workflow_runtime.entities 
            WHERE entity_id = :entity_id
            """
            
            result = self.db.execute(text(query), {"entity_id": entity_id}).fetchone()
            
            if result and result.table_name:
                return result.table_name
            else:
                # Fallback to entity_id if not found
                self.log_warning(f"No table_name found for entity_id {entity_id}, using entity_id as fallback")
                return entity_id.lower()
                
        except Exception as e:
            self.log_error(f"Error looking up table name for entity {entity_id}: {str(e)}")
            # Fallback to entity_id
            return entity_id.lower()
