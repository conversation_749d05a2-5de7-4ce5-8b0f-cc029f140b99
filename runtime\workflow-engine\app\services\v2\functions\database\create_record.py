"""
V2 Create Record Function

Database function to create records in any table with flexible input handling.
This implements the V2 pattern for database CREATE operations.
"""

from typing import Dict, Any, Optional
from sqlalchemy.sql import text

from ...base_function import BaseSystemFunction
from ...models import SystemFunctionInput, FunctionExecutionContext, FunctionCategory

class CreateRecordFunction(BaseSystemFunction):
    """
    V2 Create Record function with standardized input handling.
    
    This function can create records in any table with flexible input processing.
    It properly handles all input parameters and converts them to INSERT statements.
    """
    
    def get_category(self) -> FunctionCategory:
        return FunctionCategory.DATABASE
    
    def get_required_inputs(self) -> list[str]:
        """Required inputs for create_record function"""
        return ["table"]  # Only table is required
    
    def get_optional_inputs(self) -> list[str]:
        """Optional inputs for create_record function"""
        return [
            "data",              # Dict of column: value data to insert
            "return_columns",    # Columns to return after insert
            "on_conflict",       # Conflict resolution strategy
            
            # Legacy parameter support (for backward compatibility)
            "entity",            # Maps to table
            "entity_id",         # Can be used as primary key
            
            # Dynamic data parameters (any other parameter becomes insert data)
        ]
    
    def execute_function(self, input_data: SystemFunctionInput, context: FunctionExecutionContext) -> Dict[str, Any]:
        """
        Execute create_record with flexible parameter handling.
        
        This function intelligently handles all input parameters and converts them
        to appropriate INSERT statements.
        """
        self.log_info("🔍 Starting create_record execution", context)
        
        # Get table name (required)
        table = self.get_input_value(input_data, "table")
        if not table:
            # Try legacy 'entity' parameter
            table = self.get_input_value(input_data, "entity")
        
        if not table:
            raise ValueError("Table name is required (provide 'table' or 'entity' parameter)")
        
        self.log_info(f"📋 Target table: {table}", context)
        
        # Build the insert data
        insert_data = self._build_insert_data(input_data, context)
        
        if not insert_data:
            raise ValueError("No data provided for insert operation")
        
        # Build the INSERT query
        query_parts = self._build_insert_query(table, insert_data, input_data, context)
        
        # Execute query
        self.log_info(f"🔍 Executing query: {query_parts['query']}", context)
        self.log_info(f"🔍 Query parameters: {query_parts['parameters']}", context)
        
        try:
            result = self.db.execute(text(query_parts['query']), query_parts['parameters'])
            
            # Handle return data
            if query_parts['has_returning']:
                # Fetch the returned row
                returned_row = result.fetchone()
                if returned_row:
                    if hasattr(returned_row, '_asdict'):
                        return_data = returned_row._asdict()
                    else:
                        return_data = dict(returned_row)
                    
                    self.log_info(f"✅ Record created in {table} with returned data: {return_data}", context)
                    return return_data
                else:
                    self.log_info(f"✅ Record created in {table} (no data returned)", context)
                    return {"status": "created", "table": table}
            else:
                # No RETURNING clause, just confirm creation
                rows_affected = result.rowcount
                self.log_info(f"✅ Record created in {table} ({rows_affected} rows affected)", context)
                return {"status": "created", "table": table, "rows_affected": rows_affected}
            
        except Exception as e:
            self.log_error(f"💥 Database insert failed: {str(e)}", context)
            raise
    
    def _build_insert_data(self, input_data: SystemFunctionInput, context: FunctionExecutionContext) -> Dict[str, Any]:
        """Build insert data from input parameters"""
        
        insert_data = {}
        
        # Process explicit data parameter
        explicit_data = self.get_input_value(input_data, "data", {})
        if isinstance(explicit_data, dict):
            insert_data.update(explicit_data)
        
        # Process any other input values as potential column data
        # This handles dynamic parameters passed from LO execution
        excluded_params = {
            "table", "entity", "data", "return_columns", "on_conflict",
            "go_id", "lo_id", "user_id", "tenant_id", "instance_id",
            "primary_entity_id", "primary_attribute_id", "function_params", "execution_context"
        }
        
        for key, val in input_data.input_values.items():
            if key not in excluded_params and val is not None:
                # Convert parameter name to potential column name
                column_name = self._convert_param_to_column(key)
                insert_data[column_name] = val
                self.log_debug(f"Added insert data: {column_name} = {val}", context)
        
        self.log_info(f"📋 Insert data: {insert_data}", context)
        return insert_data
    
    def _convert_param_to_column(self, param_name: str) -> str:
        """
        Convert parameter name to database column name.
        Use parameter names as-is (lowercase) without snake_case conversion.
        """
        # Simply convert to lowercase without snake_case conversion
        return param_name.lower()
    
    def _build_insert_query(self, table: str, insert_data: Dict[str, Any], input_data: SystemFunctionInput, context: FunctionExecutionContext) -> Dict[str, Any]:
        """Build the INSERT query from data"""
        
        if not insert_data:
            raise ValueError("No data to insert")
        
        # Add audit columns automatically
        insert_data_with_audit = insert_data.copy()
        
        # Get user_id from context for audit fields
        user_id = context.user_id if hasattr(context, 'user_id') and context.user_id else 'system'
        
        # Add audit columns if they don't exist
        if 'created_at' not in insert_data_with_audit:
            insert_data_with_audit['created_at'] = 'NOW()'
        if 'created_by' not in insert_data_with_audit:
            insert_data_with_audit['created_by'] = user_id
        if 'updated_at' not in insert_data_with_audit:
            insert_data_with_audit['updated_at'] = 'NOW()'
        if 'updated_by' not in insert_data_with_audit:
            insert_data_with_audit['updated_by'] = user_id
        
        self.log_info(f"📋 Insert data with audit: {insert_data_with_audit}", context)
        
        # Build column and value lists
        columns = []
        placeholders = []
        parameters = {}
        param_counter = 0
        
        for column, value in insert_data_with_audit.items():
            columns.append(column)
            
            # Handle special SQL functions like NOW()
            if value == 'NOW()':
                placeholders.append('NOW()')
            else:
                param_name = f"param_{param_counter}"
                placeholders.append(f":{param_name}")
                
                # CRITICAL FIX: JSON serialize complex data types for PostgreSQL
                serialized_value = self._serialize_for_database(value, context)
                parameters[param_name] = serialized_value
                param_counter += 1
        
        # Map entity ID to actual table name
        actual_table_name = self._get_actual_table_name(table)
        
        # Build base INSERT query
        query = f"""
        INSERT INTO workflow_runtime.{actual_table_name} ({', '.join(columns)})
        VALUES ({', '.join(placeholders)})
        """
        
        # Add RETURNING clause if requested
        return_columns = self.get_input_value(input_data, "return_columns")
        has_returning = False
        
        if return_columns:
            if isinstance(return_columns, str):
                return_columns = [return_columns]
            elif return_columns is True:
                return_columns = ["*"]
            
            if return_columns:
                query += f" RETURNING {', '.join(return_columns)}"
                has_returning = True
        
        return {
            "query": query,
            "parameters": parameters,
            "has_returning": has_returning
        }
    
    def _get_actual_table_name(self, entity_id: str) -> str:
        """Get actual table name from entities table using entity_id"""
        try:
            # Query the entities table to get the actual table name
            query = """
            SELECT table_name FROM workflow_runtime.entities 
            WHERE entity_id = :entity_id
            """
            
            result = self.db.execute(text(query), {"entity_id": entity_id}).fetchone()
            
            if result and result.table_name:
                return result.table_name
            else:
                # Fallback to entity_id if not found
                self.log_warning(f"No table_name found for entity_id {entity_id}, using entity_id as fallback")
                return entity_id.lower()
                
        except Exception as e:
            self.log_error(f"Error looking up table name for entity {entity_id}: {str(e)}")
            # Fallback to entity_id
            return entity_id.lower()
    
    def _serialize_for_database(self, value: Any, context: FunctionExecutionContext) -> Any:
        """Serialize complex data types for PostgreSQL insertion"""
        import json
        from datetime import datetime, date
        from decimal import Decimal
        
        if value is None:
            return None
        elif isinstance(value, (str, int, float, bool)):
            # Simple types - pass through
            return value
        elif isinstance(value, (list, dict)):
            # Complex types - JSON serialize
            try:
                # Handle nested datetime/date/decimal objects
                def json_serializer(obj):
                    if isinstance(obj, (datetime, date)):
                        return obj.isoformat()
                    elif isinstance(obj, Decimal):
                        return float(obj)
                    raise TypeError(f"Object of type {type(obj)} is not JSON serializable")
                
                serialized = json.dumps(value, default=json_serializer)
                self.log_debug(f"JSON serialized complex value: {type(value)} -> {serialized[:100]}...", context)
                return serialized
            except Exception as e:
                self.log_error(f"Failed to JSON serialize value {type(value)}: {str(e)}", context)
                # Fallback to string representation
                return str(value)
        elif isinstance(value, (datetime, date)):
            # Date/datetime objects - convert to ISO format
            return value.isoformat()
        elif isinstance(value, Decimal):
            # Decimal objects - convert to float
            return float(value)
        else:
            # Other types - convert to string
            self.log_debug(f"Converting {type(value)} to string for database", context)
            return str(value)
