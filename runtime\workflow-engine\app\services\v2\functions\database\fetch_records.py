"""
V2 Fetch Records Function

Database function to fetch records from any table with flexible filtering and parameters.
This replaces the problematic v1 fetch_records function with proper parameter handling.
"""

from typing import Dict, Any, List, Optional
from sqlalchemy.sql import text

from ...base_function import BaseSystemFunction
from ...models import SystemFunctionInput, FunctionExecutionContext, FunctionCategory

class FetchRecordsFunction(BaseSystemFunction):
    """
    V2 Fetch Records function with standardized input handling.
    
    This function can fetch records from any table with flexible filtering.
    It properly handles all input parameters without type mismatches.
    """
    
    def get_category(self) -> FunctionCategory:
        return FunctionCategory.DATABASE
    
    def get_required_inputs(self) -> list[str]:
        """Required inputs for fetch_records function"""
        return []  # No required inputs - table can be derived from entity
    
    def get_optional_inputs(self) -> list[str]:
        """Optional inputs for fetch_records function"""
        return [
            "table",             # Table name (can be derived from entity)
            "filters",           # Dict of column: value filters
            "columns",           # List of columns to select
            "limit",             # Number of records to fetch
            "offset",            # Offset for pagination
            "order_by",          # Column to order by
            "order_direction",   # ASC or DESC
            "where_clause",      # Custom WHERE clause
            "join_tables",       # Tables to join
            "group_by",          # GROUP BY clause
            "having",            # HAVING clause
            
            # Legacy parameter support (for backward compatibility)
            "entity",            # Maps to table
            "attribute",         # Can be used in filters
            "value",             # Can be used in filters
            
            # Dynamic filter parameters (any other parameter becomes a filter)
        ]
    
    def execute_function(self, input_data: SystemFunctionInput, context: FunctionExecutionContext) -> Any:
        """
        Execute fetch_records with flexible parameter handling.
        
        This function intelligently handles all input parameters and converts them
        to appropriate database queries without type mismatches.
        
        CRITICAL: For nested functions with specific output requirements,
        this function can return individual field values instead of full records.
        """
        self.log_info("🔍 Starting fetch_records execution", context)
        
        # Get table name (required)
        table = self.get_input_value(input_data, "table")
        if not table:
            # Try legacy 'entity' parameter
            entity = self.get_input_value(input_data, "entity")
            if entity:
                # Map entity to table name automatically
                table = self._map_entity_to_table(entity)
                self.log_info(f"🔍 Mapped entity {entity} to table {table}", context)
        
        if not table:
            raise ValueError("Table name is required (provide 'table' or 'entity' parameter)")
        
        self.log_info(f"📋 Target table: {table}", context)
        
        # Build the query dynamically
        query_parts = self._build_query_parts(input_data, table, context)
        
        # Construct final query
        final_query = self._construct_final_query(query_parts, context)
        
        # Execute query
        self.log_info(f"🔍 Executing query: {final_query}", context)
        self.log_info(f"🔍 Query parameters: {query_parts['parameters']}", context)
        
        try:
            result = self.db.execute(text(final_query), query_parts['parameters']).fetchall()
            
            # Convert result to list of dictionaries
            records = []
            for row in result:
                if hasattr(row, '_asdict'):
                    records.append(row._asdict())
                else:
                    records.append(dict(row))
            
            self.log_info(f"✅ Fetched {len(records)} records from {table}", context)
            
            # CRITICAL FIX: Check if this is a nested function context that needs individual field extraction
            # This handles the case where GO1.LO6.NF1 needs firstName/lastName and GO1.LO6.NF2 needs email
            if context and hasattr(context, 'nested_function_id') and context.nested_function_id:
                extracted_result = self._extract_field_for_nested_function(records, context)
                if extracted_result is not None:
                    self.log_info(f"🔍 FIELD EXTRACTION: Returning extracted field value: {extracted_result}", context)
                    return extracted_result
            
            return records
            
        except Exception as e:
            self.log_error(f"💥 Database query failed: {str(e)}", context)
            raise
    
    def _build_query_parts(self, input_data: SystemFunctionInput, table: str, context: FunctionExecutionContext) -> Dict[str, Any]:
        """Build query parts from input parameters"""
        
        # Get basic query components
        columns = self.get_input_value(input_data, "columns", ["*"])
        if isinstance(columns, str):
            columns = [columns]
        
        # Build SELECT clause
        select_clause = ", ".join(columns)
        
        # Build WHERE clause and parameters
        where_conditions = []
        parameters = {}
        param_counter = 1
        
        # Process explicit filters
        filters = self.get_input_value(input_data, "filters", {})
        if isinstance(filters, dict):
            for column, value in filters.items():
                param_name = f"param_{param_counter}"
                where_conditions.append(f"{column} = :{param_name}")
                parameters[param_name] = value
                param_counter += 1
        
        # Process legacy attribute/value parameters
        attribute = self.get_input_value(input_data, "attribute")
        value = self.get_input_value(input_data, "value")
        if attribute and value is not None:
            param_name = f"param_{param_counter}"
            where_conditions.append(f"{attribute} = :{param_name}")
            parameters[param_name] = value
            param_counter += 1
        
        # Process any other input values as potential filters
        # This handles dynamic parameters passed from nested functions
        for key, val in input_data.input_values.items():
            if key not in ["table", "entity", "columns", "filters", "limit", "offset", 
                          "order_by", "order_direction", "where_clause", "join_tables", 
                          "group_by", "having", "attribute", "value"] and val is not None:
                
                # Convert parameter name to potential column name
                column_name = self._convert_param_to_column(key)
                param_name = f"param_{param_counter}"
                where_conditions.append(f"{column_name} = :{param_name}")
                parameters[param_name] = val
                param_counter += 1
                self.log_debug(f"Added dynamic filter: {column_name} = {val}", context)
        
        # Add custom WHERE clause if provided
        custom_where = self.get_input_value(input_data, "where_clause")
        if custom_where:
            where_conditions.append(f"({custom_where})")
        
        # Build ORDER BY clause
        order_clause = ""
        order_by = self.get_input_value(input_data, "order_by")
        if order_by:
            order_direction = self.get_input_value(input_data, "order_direction", "ASC")
            order_clause = f"ORDER BY {order_by} {order_direction}"
        
        # Build LIMIT clause
        limit_clause = ""
        limit = self.get_input_value(input_data, "limit")
        offset = self.get_input_value(input_data, "offset")
        if limit:
            limit_clause = f"LIMIT {limit}"
            if offset:
                limit_clause += f" OFFSET {offset}"
        
        return {
            "select_clause": select_clause,
            "table": table,
            "where_conditions": where_conditions,
            "order_clause": order_clause,
            "limit_clause": limit_clause,
            "parameters": parameters
        }
    
    def _convert_param_to_column(self, param_name: str) -> str:
        """
        Convert parameter name to database column name.
        Use parameter name as-is without snake_case conversion.
        """
        # Return the parameter name as-is (no snake_case conversion)
        return param_name.lower()
    
    def _construct_final_query(self, query_parts: Dict[str, Any], context: FunctionExecutionContext) -> str:
        """Construct the final SQL query from parts"""
        
        query = f"SELECT {query_parts['select_clause']} FROM workflow_runtime.{query_parts['table']}"
        
        if query_parts['where_conditions']:
            query += f" WHERE {' AND '.join(query_parts['where_conditions'])}"
        
        if query_parts['order_clause']:
            query += f" {query_parts['order_clause']}"
        
        if query_parts['limit_clause']:
            query += f" {query_parts['limit_clause']}"
        
        return query
    
    def _extract_field_for_nested_function(self, records: List[Dict[str, Any]], context: FunctionExecutionContext) -> Any:
        """
        Extract specific field values for nested function contexts.
        
        This method checks the nested function's output requirements and extracts
        the appropriate field values instead of returning the full record.
        """
        if not records or not context.nested_function_id:
            return None
        
        try:
            # Get the output items for this nested function to determine what field to extract
            query = """
            SELECT slot_id FROM workflow_runtime.lo_nested_function_output_items 
            WHERE nested_function_id = :nested_function_id
            ORDER BY item_id
            """
            
            output_items = self.db.execute(text(query), {
                "nested_function_id": context.nested_function_id
            }).fetchall()
            
            if not output_items:
                self.log_info(f"🔍 No output items found for {context.nested_function_id}, returning full records", context)
                return None
            
            # If there's only one output item, extract that specific field
            if len(output_items) == 1:
                slot_id = output_items[0].slot_id
                if "." in slot_id:
                    field_name = slot_id.split(".")[-1]  # Get field name from slot_id like "Employee.email" -> "email"
                    
                    # Extract the field from the first record
                    if records and isinstance(records[0], dict):
                        first_record = records[0]
                        
                        # Try exact field name
                        if field_name in first_record:
                            field_value = first_record[field_name]
                            self.log_info(f"🔍 EXTRACTED FIELD: {field_name} = {field_value} for {context.nested_function_id}", context)
                            return field_value
                        
                        # Try lowercase
                        elif field_name.lower() in first_record:
                            field_value = first_record[field_name.lower()]
                            self.log_info(f"🔍 EXTRACTED FIELD (lowercase): {field_name.lower()} = {field_value} for {context.nested_function_id}", context)
                            return field_value
                        
                        # Try case-insensitive search
                        else:
                            for key in first_record.keys():
                                if key.lower() == field_name.lower():
                                    field_value = first_record[key]
                                    self.log_info(f"🔍 EXTRACTED FIELD (case-insensitive): {key} = {field_value} for {context.nested_function_id}", context)
                                    return field_value
                            
                            self.log_info(f"🔍 Field {field_name} not found in record for {context.nested_function_id}. Available fields: {list(first_record.keys())}", context)
                            return None
            
            # If multiple output items, return the full record (let the caller handle extraction)
            else:
                self.log_info(f"🔍 Multiple output items for {context.nested_function_id}, returning full records", context)
                return None
                
        except Exception as e:
            self.log_error(f"Error extracting field for nested function {context.nested_function_id}: {str(e)}", context)
            return None
    
    def _map_entity_to_table(self, entity_id: str) -> str:
        """
        Map entity ID to table name by querying the entities table.
        This dynamically gets the table name from the database.
        """
        try:
            # Query the entities table to get the table name for this entity
            query = "SELECT table_name FROM workflow_runtime.entities WHERE entity_id = :entity_id"
            result = self.db.execute(text(query), {"entity_id": entity_id}).fetchone()
            
            if result and result.table_name:
                return result.table_name
            else:
                # Fallback: use entity_id as table name if not found
                return entity_id.lower()
                
        except Exception as e:
            # Fallback: use entity_id as table name if query fails
            return entity_id.lower()
