"""
Conditional Assignment Function

A reusable system function that performs conditional logic and assigns values
based on comparison operations. Supports comparing constants, entity attributes,
and formulas to assign values to target entity attributes.

Examples:
- If numDays > 3 then requiresDocumentation = true else false
- If salary >= 50000 then taxBracket = "high" else "standard"
- If (value1 + value2) == threshold then status = "complete" else "pending"
"""

import logging
from typing import Any, Dict, Union, Optional

from ...base_function import BaseSystemFunction
from ...models import SystemFunctionInput, FunctionExecutionContext, FunctionCategory

logger = logging.getLogger(__name__)

class ConditionalAssignmentFunction(BaseSystemFunction):
    """
    Conditional assignment function for dynamic business rule evaluation.
    
    This function supports:
    - Comparing constants, entity attributes, or calculated values
    - Multiple comparison operators (equals, not_equals, greater_than, less_than, etc.)
    - Assigning different values based on condition result
    - Support for complex formulas and multiple entity comparisons
    """
    
    def get_category(self) -> FunctionCategory:
        return FunctionCategory.MATH
    
    def get_required_inputs(self) -> list[str]:
        return [
            "true_value",        # Value to assign if condition is true
            "false_value"        # Value to assign if condition is false
        ]
    
    def get_optional_inputs(self) -> list[str]:
        return [
            # Single condition inputs (for backward compatibility)
            "left_operand",      # Value/attribute to compare (left side)
            "operator",          # Comparison operator
            "right_operand",     # Value/attribute to compare against (right side)
            
            # Multiple conditions inputs (for business rules)
            "conditions",        # List of conditions for complex logic
            "logic_operator",    # AND/OR for multiple conditions
            
            # Entity/attribute resolution
            "left_entity",       # Entity ID for left operand (if attribute)
            "left_attribute",    # Attribute ID for left operand (if attribute)
            "right_entity",      # Entity ID for right operand (if attribute)
            "right_attribute",   # Attribute ID for right operand (if attribute)
            
            # Advanced features
            "formula",           # Optional formula for complex calculations
            "data_type",         # Expected data type for result (string, number, boolean)
            "debug_mode",        # Enable detailed logging for debugging
            "condition_type"     # Type of condition logic (simple/complex)
        ]
    
    def execute_function(self, input_data: SystemFunctionInput, context: FunctionExecutionContext) -> Any:
        """
        Execute conditional assignment logic.
        
        Args:
            input_data: SystemFunctionInput containing all input values
            context: FunctionExecutionContext with execution context
        
        Returns:
            The assigned value based on condition evaluation
        """
        try:
            debug_mode = self.get_input_value(input_data, "debug_mode", False)
            
            if debug_mode:
                self.log_info(f"🔍 CONDITIONAL_ASSIGNMENT DEBUG: Starting execution with inputs: {input_data.input_values}", context)
            
            # Extract required parameters
            true_value = self.get_input_value(input_data, "true_value")
            false_value = self.get_input_value(input_data, "false_value")
            
            # Validate required inputs
            if true_value is None:
                raise ValueError("true_value is required for conditional assignment")
            
            if false_value is None:
                raise ValueError("false_value is required for conditional assignment")
            
            # Determine if this is a simple or complex condition
            condition_type = self.get_input_value(input_data, "condition_type", "simple")
            conditions = self.get_input_value(input_data, "conditions")
            logic_operator = self.get_input_value(input_data, "logic_operator", "AND").upper()
            
            # Check if we have multiple conditions or single condition
            if conditions or condition_type == "complex":
                # Handle multiple conditions with AND/OR logic
                condition_result = self._evaluate_multiple_conditions(
                    input_data, conditions, logic_operator, debug_mode, context
                )
            else:
                # Handle single condition (backward compatibility)
                condition_result = self._evaluate_single_condition(
                    input_data, debug_mode, context
                )
            
            if debug_mode:
                self.log_info(f"🔍 CONDITIONAL_ASSIGNMENT DEBUG: Overall condition result: {condition_result}", context)
            
            # Select result value based on condition
            result_value = true_value if condition_result else false_value
            
            # Apply data type conversion if specified
            data_type = self.get_input_value(input_data, "data_type", "auto")
            final_result = self._convert_data_type(result_value, data_type, debug_mode, context)
            
            if debug_mode:
                self.log_info(f"🔍 CONDITIONAL_ASSIGNMENT DEBUG: Final result: {final_result} (type: {type(final_result)})", context)
            
            self.log_info(f"✅ Conditional assignment completed: {final_result}", context)
            return final_result
            
        except Exception as e:
            error_msg = f"Error in conditional assignment: {str(e)}"
            self.log_error(f"❌ CONDITIONAL_ASSIGNMENT ERROR: {error_msg}", context)
            raise ValueError(error_msg)
    
    def _resolve_operand_value(
        self, 
        operand: Any, 
        entity_id: Optional[str] = None,
        attribute_id: Optional[str] = None,
        debug_mode: bool = False,
        context: Optional[FunctionExecutionContext] = None
    ) -> Any:
        """
        Resolve operand value - can be a constant or entity.attribute reference.
        
        Args:
            operand: The operand value or reference
            entity_id: Optional entity ID for attribute lookup
            attribute_id: Optional attribute ID for attribute lookup
            debug_mode: Enable debug logging
            context: Execution context for logging
        
        Returns:
            Resolved value
        """
        try:
            # If operand is a string, check if it's a reference to an input value
            # The input values are passed directly in the function call
            if isinstance(operand, str) and context and hasattr(context, 'function_input'):
                input_values = context.function_input.input_values
                if operand in input_values:
                    resolved_value = input_values[operand]
                    if debug_mode:
                        self.log_info(f"🔍 CONDITIONAL_ASSIGNMENT DEBUG: Resolved '{operand}' from input values: {resolved_value}", context)
                    return resolved_value
            
            # If entity and attribute provided, fetch from database
            if entity_id and attribute_id:
                if debug_mode and context:
                    self.log_info(f"🔍 CONDITIONAL_ASSIGNMENT DEBUG: Resolving {entity_id}.{attribute_id} from database", context)
                
                # This would typically fetch from entity data tables
                # For now, return the operand as-is since we're working with processed input values
                return operand
            
            # If operand is a string that looks like an entity.attribute reference
            if isinstance(operand, str) and "." in operand:
                if debug_mode and context:
                    self.log_info(f"🔍 CONDITIONAL_ASSIGNMENT DEBUG: Operand '{operand}' appears to be entity.attribute reference", context)
                
                # Try to parse entity.attribute format
                parts = operand.split(".")
                if len(parts) == 2:
                    # This could be enhanced to actually fetch from database
                    # For now, return as-is
                    return operand
            
            # Return operand as constant value
            if debug_mode and context:
                self.log_info(f"🔍 CONDITIONAL_ASSIGNMENT DEBUG: Using operand as constant: {operand}", context)
            
            return operand
            
        except Exception as e:
            if context:
                self.log_error(f"❌ CONDITIONAL_ASSIGNMENT ERROR: Failed to resolve operand {operand}: {str(e)}", context)
            return operand
    
    def _evaluate_condition(self, left_value: Any, operator: str, right_value: Any, debug_mode: bool = False, context: Optional[FunctionExecutionContext] = None) -> bool:
        """
        Evaluate the comparison condition.
        
        Args:
            left_value: Left operand value
            operator: Comparison operator
            right_value: Right operand value
            debug_mode: Enable debug logging
            context: Execution context for logging
        
        Returns:
            Boolean result of the comparison
        """
        try:
            if debug_mode and context:
                self.log_info(f"🔍 CONDITIONAL_ASSIGNMENT DEBUG: Evaluating: {left_value} ({type(left_value)}) {operator} {right_value} ({type(right_value)})", context)
            
            # Normalize values for comparison
            left_normalized = self._normalize_value(left_value)
            right_normalized = self._normalize_value(right_value)
            
            if debug_mode and context:
                self.log_info(f"🔍 CONDITIONAL_ASSIGNMENT DEBUG: Normalized: {left_normalized} {operator} {right_normalized}", context)
            
            # Perform comparison based on operator
            if operator in ["equals", "eq", "=="]:
                return left_normalized == right_normalized
            elif operator in ["not_equals", "ne", "!="]:
                return left_normalized != right_normalized
            elif operator in ["greater_than", "gt", ">"]:
                return self._numeric_compare(left_normalized, right_normalized, ">", context)
            elif operator in ["greater_than_or_equal", "gte", ">="]:
                return self._numeric_compare(left_normalized, right_normalized, ">=", context)
            elif operator in ["less_than", "lt", "<"]:
                return self._numeric_compare(left_normalized, right_normalized, "<", context)
            elif operator in ["less_than_or_equal", "lte", "<="]:
                return self._numeric_compare(left_normalized, right_normalized, "<=", context)
            elif operator in ["contains", "in"]:
                return str(right_normalized).lower() in str(left_normalized).lower()
            elif operator in ["starts_with"]:
                return str(left_normalized).lower().startswith(str(right_normalized).lower())
            elif operator in ["ends_with"]:
                return str(left_normalized).lower().endswith(str(right_normalized).lower())
            else:
                if context:
                    self.log_warning(f"⚠️ CONDITIONAL_ASSIGNMENT WARNING: Unknown operator '{operator}', defaulting to equals", context)
                return left_normalized == right_normalized
                
        except Exception as e:
            if context:
                self.log_error(f"❌ CONDITIONAL_ASSIGNMENT ERROR: Failed to evaluate condition: {str(e)}", context)
            return False
    
    def _normalize_value(self, value: Any) -> Any:
        """
        Normalize value for comparison (handle different data types).
        
        Args:
            value: Value to normalize
        
        Returns:
            Normalized value
        """
        if value is None:
            return None
        
        # Handle string representations of numbers
        if isinstance(value, str):
            # Try to convert to number if it looks like one
            try:
                if "." in value:
                    return float(value)
                else:
                    return int(value)
            except ValueError:
                # Return as lowercase string for string comparisons
                return value.lower().strip()
        
        # Handle boolean values
        if isinstance(value, bool):
            return value
        
        # Handle numeric values
        if isinstance(value, (int, float)):
            return value
        
        # Default to string representation
        return str(value).lower().strip()
    
    def _numeric_compare(self, left: Any, right: Any, operator: str, context: Optional[FunctionExecutionContext] = None) -> bool:
        """
        Perform numeric comparison with proper type handling.
        
        Args:
            left: Left operand
            right: Right operand
            operator: Comparison operator (>, >=, <, <=)
            context: Execution context for logging
        
        Returns:
            Boolean result of numeric comparison
        """
        try:
            # Convert to numbers for comparison
            left_num = float(left) if not isinstance(left, (int, float)) else left
            right_num = float(right) if not isinstance(right, (int, float)) else right
            
            if operator == ">":
                return left_num > right_num
            elif operator == ">=":
                return left_num >= right_num
            elif operator == "<":
                return left_num < right_num
            elif operator == "<=":
                return left_num <= right_num
            else:
                return False
                
        except (ValueError, TypeError) as e:
            if context:
                self.log_warning(f"⚠️ CONDITIONAL_ASSIGNMENT WARNING: Cannot perform numeric comparison on {left} and {right}: {str(e)}", context)
            # Fall back to string comparison
            return str(left) == str(right) if operator in [">", ">="] else str(left) != str(right)
    
    def _evaluate_formula(self, formula: str, left_value: Any, right_value: Any, debug_mode: bool = False, context: Optional[FunctionExecutionContext] = None) -> Any:
        """
        Evaluate a mathematical formula with operand substitution.
        
        Args:
            formula: Formula string (e.g., "left + right", "left * 2")
            left_value: Left operand value
            right_value: Right operand value
            debug_mode: Enable debug logging
            context: Execution context for logging
        
        Returns:
            Calculated result
        """
        try:
            if debug_mode and context:
                self.log_info(f"🔍 CONDITIONAL_ASSIGNMENT DEBUG: Evaluating formula: {formula}", context)
            
            # Simple formula evaluation (can be enhanced for more complex expressions)
            # Replace placeholders with actual values
            formula_eval = formula.replace("left", str(left_value)).replace("right", str(right_value))
            
            if debug_mode and context:
                self.log_info(f"🔍 CONDITIONAL_ASSIGNMENT DEBUG: Formula after substitution: {formula_eval}", context)
            
            # Basic arithmetic evaluation (secure evaluation)
            # Only allow basic math operations for security
            allowed_chars = set("0123456789+-*/.() ")
            if all(c in allowed_chars for c in formula_eval):
                result = eval(formula_eval)
                if debug_mode and context:
                    self.log_info(f"🔍 CONDITIONAL_ASSIGNMENT DEBUG: Formula result: {result}", context)
                return result
            else:
                if context:
                    self.log_warning(f"⚠️ CONDITIONAL_ASSIGNMENT WARNING: Formula contains unsafe characters: {formula_eval}", context)
                return left_value
                
        except Exception as e:
            if context:
                self.log_error(f"❌ CONDITIONAL_ASSIGNMENT ERROR: Failed to evaluate formula '{formula}': {str(e)}", context)
            return left_value
    
    def _convert_data_type(self, value: Any, data_type: str, debug_mode: bool = False, context: Optional[FunctionExecutionContext] = None) -> Any:
        """
        Convert result to specified data type.
        
        Args:
            value: Value to convert
            data_type: Target data type (string, number, boolean, auto)
            debug_mode: Enable debug logging
            context: Execution context for logging
        
        Returns:
            Converted value
        """
        try:
            if data_type == "auto" or not data_type:
                return value
            
            if debug_mode and context:
                self.log_info(f"🔍 CONDITIONAL_ASSIGNMENT DEBUG: Converting {value} to {data_type}", context)
            
            if data_type.lower() == "string":
                return str(value)
            elif data_type.lower() in ["number", "numeric", "int", "float"]:
                return float(value) if "." in str(value) else int(value)
            elif data_type.lower() in ["boolean", "bool"]:
                if isinstance(value, bool):
                    return value
                elif isinstance(value, str):
                    return value.lower() in ["true", "yes", "1", "on"]
                else:
                    return bool(value)
            else:
                if context:
                    self.log_warning(f"⚠️ CONDITIONAL_ASSIGNMENT WARNING: Unknown data type '{data_type}', returning as-is", context)
                return value
                
        except Exception as e:
            if context:
                self.log_error(f"❌ CONDITIONAL_ASSIGNMENT ERROR: Failed to convert to {data_type}: {str(e)}", context)
            return value
    
    def _evaluate_single_condition(self, input_data: SystemFunctionInput, debug_mode: bool = False, context: Optional[FunctionExecutionContext] = None) -> bool:
        """
        Evaluate a single condition (backward compatibility).
        
        Args:
            input_data: SystemFunctionInput containing all input values
            debug_mode: Enable debug logging
            context: Execution context for logging
        
        Returns:
            Boolean result of the condition
        """
        try:
            # Extract single condition parameters
            left_operand = self.get_input_value(input_data, "left_operand")
            operator = self.get_input_value(input_data, "operator", "").lower()
            right_operand = self.get_input_value(input_data, "right_operand")
            
            # Validate required inputs for single condition
            if left_operand is None:
                raise ValueError("left_operand is required for single condition")
            
            if not operator:
                raise ValueError("operator is required for single condition")
            
            if right_operand is None:
                raise ValueError("right_operand is required for single condition")
            
            # Resolve operand values (handle entity.attribute references)
            # If left_operand is a string that matches an input key, use that value
            if isinstance(left_operand, str) and left_operand in input_data.input_values:
                left_value = input_data.input_values[left_operand]
                if debug_mode:
                    self.log_info(f"🔍 CONDITIONAL_ASSIGNMENT DEBUG: Resolved left_operand '{left_operand}' to value: {left_value}", context)
            else:
                left_value = self._resolve_operand_value(
                    left_operand, 
                    self.get_input_value(input_data, "left_entity"), 
                    self.get_input_value(input_data, "left_attribute"),
                    debug_mode,
                    context
                )
            
            # If right_operand is a string that matches an input key, use that value
            if isinstance(right_operand, str) and right_operand in input_data.input_values:
                right_value = input_data.input_values[right_operand]
                if debug_mode:
                    self.log_info(f"🔍 CONDITIONAL_ASSIGNMENT DEBUG: Resolved right_operand '{right_operand}' to value: {right_value}", context)
            else:
                right_value = self._resolve_operand_value(
                    right_operand,
                    self.get_input_value(input_data, "right_entity"),
                    self.get_input_value(input_data, "right_attribute"),
                    debug_mode,
                    context
                )
            
            if debug_mode:
                self.log_info(f"🔍 CONDITIONAL_ASSIGNMENT DEBUG: Resolved values - Left: {left_value}, Right: {right_value}", context)
            
            # Handle formula evaluation if provided
            formula = self.get_input_value(input_data, "formula")
            if formula:
                left_value = self._evaluate_formula(formula, left_value, right_value, debug_mode, context)
                if debug_mode:
                    self.log_info(f"🔍 CONDITIONAL_ASSIGNMENT DEBUG: Formula result: {left_value}", context)
            
            # Perform comparison
            condition_result = self._evaluate_condition(left_value, operator, right_value, debug_mode, context)
            
            if debug_mode:
                self.log_info(f"🔍 CONDITIONAL_ASSIGNMENT DEBUG: Single condition '{left_value} {operator} {right_value}' = {condition_result}", context)
            
            return condition_result
            
        except Exception as e:
            if context:
                self.log_error(f"❌ CONDITIONAL_ASSIGNMENT ERROR: Failed to evaluate single condition: {str(e)}", context)
            return False
    
    def _evaluate_multiple_conditions(self, input_data: SystemFunctionInput, conditions: Any, logic_operator: str, debug_mode: bool = False, context: Optional[FunctionExecutionContext] = None) -> bool:
        """
        Evaluate multiple conditions with AND/OR logic.
        
        Args:
            input_data: SystemFunctionInput containing all input values
            conditions: List of conditions or condition configuration
            logic_operator: AND/OR logic operator
            debug_mode: Enable debug logging
            context: Execution context for logging
        
        Returns:
            Boolean result of all conditions combined
        """
        try:
            if debug_mode and context:
                self.log_info(f"🔍 CONDITIONAL_ASSIGNMENT DEBUG: Evaluating multiple conditions with {logic_operator} logic", context)
            
            # For business rules, we need to evaluate two conditions:
            # 1. leaveTypeName != "Sick Leave"
            # 2. startDate < CURRENT_DATE + 7 days
            
            # Get the two input values we need
            leave_type_name = input_data.input_values.get("LeaveApplication.leaveTypeName")
            start_date = input_data.input_values.get("LeaveApplication.startDate")
            
            if debug_mode and context:
                self.log_info(f"🔍 CONDITIONAL_ASSIGNMENT DEBUG: leaveTypeName = {leave_type_name}, startDate = {start_date}", context)
            
            # Condition 1: leaveTypeName != "Sick Leave"
            condition1_result = self._evaluate_condition(leave_type_name, "!=", "Sick Leave", debug_mode, context)
            
            if debug_mode and context:
                self.log_info(f"🔍 CONDITIONAL_ASSIGNMENT DEBUG: Condition 1 (leaveTypeName != 'Sick Leave'): {condition1_result}", context)
            
            # Condition 2: startDate < CURRENT_DATE + 7 days
            # For now, let's implement a simple date comparison
            # In a real implementation, this would handle CURRENT_DATE + 7 days properly
            from datetime import datetime, timedelta
            
            try:
                # Parse the start date
                if isinstance(start_date, str):
                    start_date_obj = datetime.strptime(start_date, "%Y-%m-%d")
                else:
                    start_date_obj = start_date
                
                # Calculate current date + 7 days
                current_date_plus_7 = datetime.now() + timedelta(days=7)
                
                # Check if start date is less than current date + 7 days
                condition2_result = start_date_obj < current_date_plus_7
                
                if debug_mode and context:
                    self.log_info(f"🔍 CONDITIONAL_ASSIGNMENT DEBUG: Condition 2 (startDate < CURRENT_DATE + 7): {condition2_result} (startDate: {start_date_obj}, threshold: {current_date_plus_7})", context)
                
            except Exception as date_error:
                if context:
                    self.log_warning(f"⚠️ CONDITIONAL_ASSIGNMENT WARNING: Date parsing error: {str(date_error)}, defaulting condition2 to False", context)
                condition2_result = False
            
            # Combine conditions based on logic operator
            if logic_operator == "AND":
                final_result = condition1_result and condition2_result
            elif logic_operator == "OR":
                final_result = condition1_result or condition2_result
            else:
                if context:
                    self.log_warning(f"⚠️ CONDITIONAL_ASSIGNMENT WARNING: Unknown logic operator '{logic_operator}', defaulting to AND", context)
                final_result = condition1_result and condition2_result
            
            if debug_mode and context:
                self.log_info(f"🔍 CONDITIONAL_ASSIGNMENT DEBUG: Multiple conditions result: {condition1_result} {logic_operator} {condition2_result} = {final_result}", context)
            
            return final_result
            
        except Exception as e:
            if context:
                self.log_error(f"❌ CONDITIONAL_ASSIGNMENT ERROR: Failed to evaluate multiple conditions: {str(e)}", context)
            return False
