"""
Math Functions Module

This module contains mathematical and logical system functions for the V2 workflow engine.
"""

from .subtract_days import SubtractDaysFunction
from .conditional_assignment import ConditionalAssignmentFunction

# Export all math functions
__all__ = [
    'SubtractDaysFunction',
    'ConditionalAssignmentFunction'
]

# Function registry for dynamic loading
MATH_FUNCTIONS = {
    'subtract_days': SubtractDaysFunction,
    'conditional_assignment': ConditionalAssignmentFunction
}

def get_math_function(function_name: str):
    """Get a math function class by name"""
    return MATH_FUNCTIONS.get(function_name)

def get_all_math_functions():
    """Get all available math functions"""
    return MATH_FUNCTIONS
